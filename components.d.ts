/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    1: typeof import('./src/components/BannerSwiper/1.vue')['default']
    10: typeof import('./src/components/BannerSwiper/slides/10.vue')['default']
    9: typeof import('./src/components/BannerSwiper/slides/9.vue')['default']
    BannerSwiper: typeof import('./src/components/BannerSwiper/index.vue')['default']
    Button: typeof import('primevue/button')['default']
    Gallery: typeof import('./src/components/Gallery/index.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    IconLocalArticle: typeof import('~icons/local/article')['default']
    IconLocalFavicon: typeof import('~icons/local/favicon')['default']
    IconLocalGithub: typeof import('~icons/local/github')['default']
    IconLocalPhoneTablet: typeof import('~icons/local/phone-tablet')['default']
    Menubar: typeof import('primevue/menubar')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Slogan: typeof import('./src/components/BannerSwiper/slides/Slogan/index.vue')['default']
  }
}
