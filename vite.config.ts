import tailwindcss from "@tailwindcss/vite";
import vue from "@vitejs/plugin-vue";
import { fileURLToPath, URL } from "node:url";
import AutoImport from "unplugin-auto-import/vite";
import { FileSystemIconLoader } from "unplugin-icons/loaders";
import IconsResolver from "unplugin-icons/resolver";
import Icons from "unplugin-icons/vite";
import { PrimeVueResolver } from "unplugin-vue-components/resolvers";
import Components from "unplugin-vue-components/vite";
import { defineConfig } from "vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    tailwindcss(),
    AutoImport({
      imports: ["vue", "vue-router"], // 自动导入vue和vue-router的API
      dts: "src/auto-imports.d.ts", // 生成类型声明文件
    }),
    // 配置 unplugin-icons 支持本地 SVG
    Icons({
      compiler: "vue3",
      customCollections: {
        local: FileSystemIconLoader("src/assets/icons"), // 注册本地 SVG 组件的加载器
      },
      scale: 1, // 可选：设置图标缩放比例
      defaultClass: "block", // 可选：设置默认类
    }),
    // 配置自动导入本地 SVG 组件
    Components({
      resolvers: [
        IconsResolver({
          customCollections: ["local"], // 注册本地 SVG 组件的解析器
          prefix: "Icon", // 确保前缀为 Icon
        }),
        PrimeVueResolver(), // 添加 PrimeVue 组件自动导入
      ],
      dts: true, // 确保生成类型声明文件
    }),
  ],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
});
