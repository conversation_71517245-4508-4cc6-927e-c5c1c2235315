import App from "@/App.vue";
import "@/assets/styles/global.scss";
import "@/assets/styles/tailwind.css";
import { definePreset } from "@primeuix/themes";
import <PERSON> from "@primeuix/themes/lara";
import { MotionPlugin } from "@vueuse/motion";
import PrimeVue from "primevue/config";
import { createApp } from "vue";

const app = createApp(App);
const MyPreset = definePreset(<PERSON>, {
  semantic: {
    primary: {
      50: "{zinc.50}",
      100: "{zinc.100}",
      200: "{zinc.200}",
      300: "{zinc.300}",
      400: "{zinc.400}",
      500: "{zinc.500}",
      600: "{zinc.600}",
      700: "{zinc.700}",
      800: "{zinc.800}",
      900: "{zinc.900}",
      950: "{zinc.950}",
    },
    colorScheme: {
      light: {
        surface: {
          0: "#ffffff",
          50: "{zinc.50}",
          100: "{zinc.100}",
          200: "{zinc.200}",
          300: "{zinc.300}",
          400: "{zinc.400}",
          500: "{zinc.500}",
          600: "{zinc.600}",
          700: "{zinc.700}",
          800: "{zinc.800}",
          900: "{zinc.900}",
          950: "{zinc.950}",
        },
      },
      dark: {
        surface: {
          0: "#ffffff",
          50: "{slate.50}",
          100: "{slate.100}",
          200: "{slate.200}",
          300: "{slate.300}",
          400: "{slate.400}",
          500: "{slate.500}",
          600: "{slate.600}",
          700: "{slate.700}",
          800: "{slate.800}",
          900: "{slate.900}",
          950: "{slate.950}",
        },
      },
    },
  },
});
app.use(PrimeVue, {
  theme: {
    preset: MyPreset,
  },
});
app.use(MotionPlugin);
app.mount("#app");
