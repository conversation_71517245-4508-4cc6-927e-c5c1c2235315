// 金属质感文字
.metal-text {
  background: linear-gradient(135deg, #e6e6e6 0%, #b3b3b3 25%, #999 50%, #b3b3b3 75%, #e6e6e6 100%);
  background-clip: text;
  color: transparent;
  text-shadow:
    /* 高光阴影 */
    0 1px 1px rgba(255, 255, 255, 0.3),
    /* 底部阴影 */ 0 -1px 1px rgba(0, 0, 0, 0.5);
  position: relative;

  /* 添加高光点增强质感 */
  &::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 50%);
    background-clip: text;
    color: transparent;
    pointer-events: none;
  }
}
