<template>
  <div class="h-[60px]">
    <div class="flex h-full w-full justify-between xl:mx-auto xl:w-[1280px]">
      <div class="flex items-center gap-2 px-4">
        <img src="@/assets/logo.png" alt="前端响应式" class="h-[32px] w-[32px]" />
        <h1 class="text-lg font-medium">前端响应式</h1>
      </div>
      <div class="flex items-center px-4 text-base">
        <Menubar :model="menubarItems" class="gap-4 !p-0">
          <template #item="{ item, props }">
            <a class="flex items-center hover:bg-blue-100 hover:!text-blue-500" v-bind="props.action">
              <span>{{ item.label }}</span>
            </a>
          </template>
        </Menubar>
      </div>
      <div class="flex items-center gap-4 px-4">
        <IconLocalPhoneTablet class="h-[18px] w-[18px] cursor-pointer" />
        <IconLocalGithub class="h-[18px] w-[18px] cursor-pointer" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const menubarItems = ref([
  {
    label: "文章",
    command: () => {
      //  router.push({ path: "/articles" });
    },
  },
  {
    label: "宝书",
    command: () => {
      //  router.push({ path: "/treasure-books" });
    },
  },
  {
    label: "工具",
    command: () => {
      //  router.push({ path: "/tools" });
    },
  },
]);
</script>
