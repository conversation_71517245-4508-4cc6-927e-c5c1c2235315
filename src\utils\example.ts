// 示例：使用 @ 前缀路径导入的工具函数

// 从 components 目录导入
// import SomeComponent from '@/components/SomeComponent.vue'

// 从 stores 目录导入
// import { useUserStore } from '@/stores/user'

// 从 composables 目录导入
// import { useApi } from '@/composables/useApi'

// 从 utils 目录导入
// import { formatDate } from '@/utils/date'

// 从 assets 目录导入
// import logo from '@/assets/images/logo.png'

export function exampleFunction() {
  console.log('这是一个使用 @ 前缀路径的示例文件');
}
