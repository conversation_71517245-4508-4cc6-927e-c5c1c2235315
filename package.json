{"name": "reactref.com-frontoffice", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@primeuix/themes": "^1.2.1", "@vueuse/motion": "^3.0.3", "pinia": "^3.0.3", "primevue": "^4.3.6", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/vite": "^4.1.11", "@types/node": "^24.0.10", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.13", "sass-embedded": "^1.89.2", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.2", "vue-tsc": "^3.0.1"}}